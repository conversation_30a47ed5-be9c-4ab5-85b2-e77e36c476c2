/**
 * Cookie管理工具类
 * 移植Python版本的Cookie处理逻辑
 */

export class CookieManager {
  /**
   * 解析Cookie字符串为对象
   * 移植自Python版本的parse_cookie()函数
   * @param {string} cookieString - Cookie字符串
   * @returns {Object} Cookie对象
   */
  static parseCookie(cookieString) {
    if (!cookieString || typeof cookieString !== 'string') {
      return {};
    }

    const cookies = {};
    
    try {
      // 按分号分割Cookie项
      const items = cookieString.trim().split(';');
      
      for (const item of items) {
        const trimmedItem = item.trim();
        if (!trimmedItem) continue;
        
        // 按等号分割键值对
        const equalIndex = trimmedItem.indexOf('=');
        if (equalIndex === -1) continue;
        
        const key = trimmedItem.substring(0, equalIndex).trim();
        const value = trimmedItem.substring(equalIndex + 1).trim();
        
        if (key) {
          cookies[key] = value;
        }
      }
    } catch (error) {
      throw new Error(`Cookie解析失败: ${error.message}`);
    }
    
    return cookies;
  }

  /**
   * 验证网易云音乐Cookie的有效性
   * @param {string} cookieString - Cookie字符串
   * @returns {boolean} 是否有效
   */
  static validateNeteaseCookie(cookieString) {
    try {
      const cookies = this.parseCookie(cookieString);
      
      // 检查必需的Cookie字段
      const requiredFields = ['MUSIC_U'];
      for (const field of requiredFields) {
        if (!cookies[field] || cookies[field].length === 0) {
          return false;
        }
      }
      
      // 检查MUSIC_U的基本格式
      const musicU = cookies['MUSIC_U'];
      if (musicU.length < 10) {
        return false;
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 验证QQ音乐Cookie的有效性
   * @param {string} cookieString - Cookie字符串
   * @returns {boolean} 是否有效
   */
  static validateQQCookie(cookieString) {
    try {
      const cookies = this.parseCookie(cookieString);
      
      // QQ音乐Cookie验证相对宽松，只要有内容即可
      return Object.keys(cookies).length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 格式化网易云音乐Cookie
   * 确保包含必要的字段
   * @param {string} cookieString - 原始Cookie字符串
   * @returns {string} 格式化后的Cookie字符串
   */
  static formatNeteaseCookie(cookieString) {
    try {
      const cookies = this.parseCookie(cookieString);
      
      // 确保包含基本字段
      const defaultFields = {
        'os': 'pc',
        'appver': '8.9.70'
      };
      
      // 合并默认字段
      const mergedCookies = { ...defaultFields, ...cookies };
      
      // 重新组装Cookie字符串
      const cookiePairs = [];
      for (const [key, value] of Object.entries(mergedCookies)) {
        cookiePairs.push(`${key}=${value}`);
      }
      
      return cookiePairs.join(';');
    } catch (error) {
      throw new Error(`网易云Cookie格式化失败: ${error.message}`);
    }
  }

  /**
   * 从环境变量获取Cookie
   * @param {Object} env - 环境变量对象
   * @param {string} platform - 平台类型 ('netease' | 'qq')
   * @returns {string} Cookie字符串
   */
  static getCookieFromEnv(env, platform) {
    switch (platform) {
      case 'netease':
        return env.NETEASE_COOKIE || '';
      case 'qq':
        return env.QQ_COOKIE || '';
      default:
        return '';
    }
  }

  /**
   * 检查Cookie是否过期（基于简单的启发式方法）
   * @param {string} cookieString - Cookie字符串
   * @returns {boolean} 是否可能过期
   */
  static isCookieExpired(cookieString) {
    try {
      const cookies = this.parseCookie(cookieString);
      
      // 检查是否有expires字段
      for (const [key, value] of Object.entries(cookies)) {
        if (key.toLowerCase().includes('expires')) {
          const expireDate = new Date(value);
          if (!isNaN(expireDate.getTime()) && expireDate < new Date()) {
            return true;
          }
        }
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 清理Cookie字符串，移除无效字符
   * @param {string} cookieString - 原始Cookie字符串
   * @returns {string} 清理后的Cookie字符串
   */
  static sanitizeCookie(cookieString) {
    if (!cookieString || typeof cookieString !== 'string') {
      return '';
    }
    
    // 移除换行符、制表符等无效字符
    return cookieString
      .replace(/[\r\n\t]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 将Cookie对象转换为字符串
   * @param {Object} cookieObject - Cookie对象
   * @returns {string} Cookie字符串
   */
  static cookieObjectToString(cookieObject) {
    if (!cookieObject || typeof cookieObject !== 'object') {
      return '';
    }
    
    const pairs = [];
    for (const [key, value] of Object.entries(cookieObject)) {
      if (key && value !== undefined && value !== null) {
        pairs.push(`${key}=${value}`);
      }
    }
    
    return pairs.join(';');
  }
}
