/**
 * 统一API路由系统
 * 整合网易云音乐和QQ音乐的API接口
 */

import { NeteaseAPI } from './apis/netease.js';
import { QQMusicAPI } from './apis/qq-music.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, URLParser, Utils } from './utils/index.js';

export class APIRouter {
  /**
   * 处理API请求的主入口
   * @param {Request} request - 请求对象
   * @param {Object} env - 环境变量
   * @returns {Promise<Response>} 响应对象
   */
  static async handleRequest(request, env) {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // 添加CORS头
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理OPTIONS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      let response;

      // 路由分发
      if (pathname.startsWith('/api/netease/')) {
        response = await this.handleNeteaseAPI(request, env, pathname);
      } else if (pathname.startsWith('/api/qq/')) {
        response = await this.handleQQAPI(request, env, pathname);
      } else if (pathname === '/health') {
        response = await this.handleHealthCheck(env);
      } else {
        response = this.createErrorResponse(404, '接口不存在');
      }

      // 添加CORS头到响应
      const responseHeaders = new Headers(response.headers);
      Object.entries(corsHeaders).forEach(([key, value]) => {
        responseHeaders.set(key, value);
      });

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders
      });

    } catch (error) {
      return this.createErrorResponse(500, `服务器内部错误: ${error.message}`);
    }
  }

  /**
   * 处理网易云音乐API请求
   * @param {Request} request - 请求对象
   * @param {Object} env - 环境变量
   * @param {string} pathname - 路径
   * @returns {Promise<Response>} 响应对象
   */
  static async handleNeteaseAPI(request, env, pathname) {
    const cookies = CookieManager.parseCookie(
      CookieManager.getCookieFromEnv(env, 'netease')
    );

    // 验证Cookie
    if (!CookieManager.validateNeteaseCookie(env.NETEASE_COOKIE || '')) {
      return this.createErrorResponse(401, '网易云Cookie无效或未配置');
    }

    const params = await this.extractParams(request);

    switch (pathname) {
      case '/api/netease/song':
        return await this.handleNeteaseSong(params, cookies);
      
      case '/api/netease/search':
        return await this.handleNeteaseSearch(params, cookies);
      
      case '/api/netease/playlist':
        return await this.handleNeteasePlaylist(params, cookies);
      
      case '/api/netease/album':
        return await this.handleNeteaseAlbum(params, cookies);
      
      default:
        return this.createErrorResponse(404, '网易云API接口不存在');
    }
  }

  /**
   * 处理QQ音乐API请求
   * @param {Request} request - 请求对象
   * @param {Object} env - 环境变量
   * @param {string} pathname - 路径
   * @returns {Promise<Response>} 响应对象
   */
  static async handleQQAPI(request, env, pathname) {
    const cookies = CookieManager.parseCookie(
      CookieManager.getCookieFromEnv(env, 'qq')
    );

    const params = await this.extractParams(request);

    switch (pathname) {
      case '/api/qq/song':
        return await this.handleQQSong(params, cookies);
      
      default:
        return this.createErrorResponse(404, 'QQ音乐API接口不存在');
    }
  }

  /**
   * 处理网易云单曲解析 - 兼容原版/Song_V1接口
   * @param {Object} params - 请求参数
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Response>} 响应对象
   */
  static async handleNeteaseSong(params, cookies) {
    try {
      // 参数验证
      const { ids, url, level, type } = params;
      
      if (!ids && !url) {
        return this.createErrorResponse(400, '必须提供 ids 或 url 参数');
      }
      if (!level) {
        return this.createErrorResponse(400, 'level参数为空');
      }
      if (!type) {
        return this.createErrorResponse(400, 'type参数为空');
      }

      // 验证音质等级
      if (!NeteaseAPI.isValidQualityLevel(level)) {
        return this.createErrorResponse(400, `无效的音质等级: ${level}`);
      }

      const jsondata = ids || url;
      const songId = await URLParser.parseNetease(jsondata);
      
      // 获取歌曲URL
      const urlResult = await NeteaseAPI.url_v1(songId, level, cookies);
      if (!urlResult.data || !urlResult.data[0] || !urlResult.data[0].url) {
        return this.createErrorResponse(400, '信息获取不完整！');
      }

      // 获取歌曲信息
      const nameResult = await NeteaseAPI.name_v1(urlResult.data[0].id);
      const lyricResult = await NeteaseAPI.lyric_v1(urlResult.data[0].id, cookies);

      const songData = urlResult.data[0];
      const songInfo = nameResult.songs?.[0] || {};
      
      // 构建响应数据
      const responseData = {
        status: 200,
        name: songInfo.name || '',
        pic: songInfo.al?.picUrl || '',
        ar_name: songInfo.ar?.map(artist => artist.name).join('/') || '',
        al_name: songInfo.al?.name || '',
        level: Utils.getMusicQualityName(songData.level),
        size: Utils.formatFileSize(songData.size),
        url: songData.url.replace('http://', 'https://'),
        lyric: lyricResult.lrc?.lyric || '',
        tlyric: lyricResult.tlyric?.lyric || null
      };

      // 根据type参数返回不同格式
      switch (type) {
        case 'json':
          return this.createSuccessResponse(responseData);
        
        case 'text':
          const textData = `歌曲名称：${responseData.name}<br>歌曲图片：${responseData.pic}<br>歌手：${responseData.ar_name}<br>歌曲专辑：${responseData.al_name}<br>歌曲音质：${responseData.level}<br>歌曲大小：${responseData.size}<br>音乐地址：${responseData.url}`;
          return new Response(textData, {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
          });
        
        case 'down':
          return Response.redirect(responseData.url, 302);
        
        default:
          return this.createErrorResponse(400, '解析失败！请检查参数是否完整！');
      }

    } catch (error) {
      return this.createErrorResponse(500, `网易云单曲解析失败: ${error.message}`);
    }
  }

  /**
   * 处理网易云搜索 - 兼容原版/Search接口
   * @param {Object} params - 请求参数
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Response>} 响应对象
   */
  static async handleNeteaseSearch(params, cookies) {
    try {
      const { keywords, limit = 10 } = params;
      
      if (!keywords) {
        return this.createErrorResponse(400, '必须提供 keywords 参数');
      }

      const songs = await NeteaseAPI.search_music(keywords, cookies, parseInt(limit));
      
      return this.createSuccessResponse({
        status: 200,
        result: songs
      });

    } catch (error) {
      return this.createErrorResponse(500, `网易云搜索失败: ${error.message}`);
    }
  }

  /**
   * 处理网易云歌单解析 - 兼容原版/Playlist接口
   * @param {Object} params - 请求参数
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Response>} 响应对象
   */
  static async handleNeteasePlaylist(params, cookies) {
    try {
      const { id } = params;
      
      if (!id) {
        return this.createErrorResponse(400, '必须提供歌单id参数');
      }

      const playlist = await NeteaseAPI.playlist_detail(id, cookies);
      
      return this.createSuccessResponse({
        status: 200,
        playlist: playlist
      });

    } catch (error) {
      return this.createErrorResponse(500, `网易云歌单解析失败: ${error.message}`);
    }
  }

  /**
   * 处理网易云专辑解析 - 兼容原版/Album接口
   * @param {Object} params - 请求参数
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Response>} 响应对象
   */
  static async handleNeteaseAlbum(params, cookies) {
    try {
      const { id } = params;
      
      if (!id) {
        return this.createErrorResponse(400, '必须提供专辑id参数');
      }

      const album = await NeteaseAPI.album_detail(id, cookies);
      
      return this.createSuccessResponse({
        status: 200,
        album: album
      });

    } catch (error) {
      return this.createErrorResponse(500, `网易云专辑解析失败: ${error.message}`);
    }
  }

  /**
   * 处理QQ音乐歌曲解析 - 兼容原版/song接口
   * @param {Object} params - 请求参数
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Response>} 响应对象
   */
  static async handleQQSong(params, cookies) {
    try {
      const { url } = params;
      
      if (!url) {
        return this.createErrorResponse(400, 'url parameter is required');
      }

      // 提取歌曲ID
      const songmid = await QQMusicAPI.extractSongId(url);
      if (!songmid) {
        return this.createErrorResponse(400, '无效的QQ音乐链接');
      }

      // 判断是songid还是songmid
      let sid = 0, mid = songmid;
      try {
        sid = parseInt(songmid);
        mid = 0;
      } catch (e) {
        sid = 0;
        mid = songmid;
      }

      // 获取歌曲信息
      const songInfo = await QQMusicAPI.getMusicSong(mid, sid, cookies);
      
      if (songInfo.msg) {
        return this.createErrorResponse(400, songInfo.msg);
      }

      // 获取所有音质的URL
      const fileTypes = QQMusicAPI.getSupportedFormats();
      const musicUrls = {};
      
      for (const fileType of fileTypes) {
        try {
          const result = await QQMusicAPI.getMusicUrl(songInfo.mid, fileType, cookies);
          if (result) {
            musicUrls[fileType] = result;
          }
          // 添加小延迟避免请求过快
          await Utils.delay(100);
        } catch (error) {
          // 忽略单个格式的错误，继续处理其他格式
          console.warn(`获取${fileType}格式失败:`, error.message);
        }
      }

      // 获取歌词
      const lyric = await QQMusicAPI.getMusicLyricNew(songInfo.id, cookies);

      const responseData = {
        song: songInfo,
        lyric: lyric,
        music_urls: musicUrls
      };

      return this.createSuccessResponse(responseData);

    } catch (error) {
      return this.createErrorResponse(500, `QQ音乐解析失败: ${error.message}`);
    }
  }

  /**
   * 健康检查接口
   * @param {Object} env - 环境变量
   * @returns {Promise<Response>} 响应对象
   */
  static async handleHealthCheck(env) {
    const healthData = {
      status: 'ok',
      version: env.APP_VERSION || '1.0.0',
      timestamp: new Date().toISOString(),
      services: {
        netease: CookieManager.validateNeteaseCookie(env.NETEASE_COOKIE || ''),
        qq: CookieManager.validateQQCookie(env.QQ_COOKIE || '')
      }
    };

    return this.createSuccessResponse(healthData);
  }

  /**
   * 提取请求参数（支持GET和POST）
   * @param {Request} request - 请求对象
   * @returns {Promise<Object>} 参数对象
   */
  static async extractParams(request) {
    const url = new URL(request.url);
    const params = {};

    // 获取URL参数
    for (const [key, value] of url.searchParams) {
      params[key] = value;
    }

    // 如果是POST请求，获取表单数据
    if (request.method === 'POST') {
      try {
        const contentType = request.headers.get('content-type') || '';
        
        if (contentType.includes('application/json')) {
          const jsonData = await request.json();
          Object.assign(params, jsonData);
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData();
          for (const [key, value] of formData) {
            params[key] = value;
          }
        }
      } catch (error) {
        // 忽略解析错误，使用URL参数
      }
    }

    return params;
  }

  /**
   * 创建成功响应
   * @param {Object} data - 响应数据
   * @returns {Response} 响应对象
   */
  static createSuccessResponse(data) {
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
  }

  /**
   * 创建错误响应
   * @param {number} status - 状态码
   * @param {string} message - 错误信息
   * @returns {Response} 响应对象
   */
  static createErrorResponse(status, message) {
    const errorData = {
      status: status,
      error: message,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(errorData), {
      status: status,
      headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
  }
}
