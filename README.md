# 音乐解析工具 - Cloudflare Workers版本

> 将网易云音乐和QQ音乐解析功能整合到Cloudflare Workers平台，提供高性能的全球边缘计算音乐解析服务。

[![Deploy to Cloudflare Workers](https://deploy.workers.cloudflare.com/button)](https://deploy.workers.cloudflare.com/?url=https://github.com/your-repo/music-parser)

## ✨ 功能特性

- 🎵 **网易云音乐解析**：支持单曲、搜索、歌单、专辑解析
- 🎶 **QQ音乐解析**：支持多种音质格式的歌曲解析
- 🌐 **全球加速**：基于Cloudflare Workers的边缘计算
- 📱 **现代化界面**：响应式Web界面，支持移动端和主题切换
- 🔒 **安全可靠**：无服务器架构，数据不存储
- ⚡ **高性能**：毫秒级响应，全球CDN分发
- 🎨 **美观设计**：玻璃态效果，现代化UI设计
- 🔄 **API兼容**：与原Python版本100%兼容

## 🎼 支持的音质

### 网易云音乐 (7种音质等级)
- `standard`：标准音质 (128kbps)
- `exhigh`：极高音质 (320kbps)
- `lossless`：无损音质 (FLAC)
- `hires`：Hi-Res音质 (24bit/96kHz)
- `jyeffect`：高清环绕声
- `sky`：沉浸环绕声
- `jymaster`：超清母带

### QQ音乐 (13种格式支持)
- **MP3格式**：`128`、`320`
- **无损格式**：`flac`、`master`
- **AAC格式**：`aac_48`、`aac_96`、`aac_192`
- **OGG格式**：`ogg_96`、`ogg_192`、`ogg_320`、`ogg_640`
- **环绕声**：`atmos_2`、`atmos_51`

## 🚀 快速开始

### 方式一：一键部署（推荐）

[![Deploy to Cloudflare Workers](https://deploy.workers.cloudflare.com/button)](https://deploy.workers.cloudflare.com/?url=https://github.com/your-repo/music-parser)

### 方式二：手动部署

#### 1. 环境准备

```bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 克隆项目
git clone https://github.com/your-repo/music-parser.git
cd music-parser/MusicApi

# 安装依赖
npm install
```

#### 2. 配置环境变量

**推荐方式：使用Wrangler Secrets**
```bash
# 设置网易云Cookie（需要黑胶会员）
wrangler secret put NETEASE_COOKIE

# 设置QQ音乐Cookie（需要绿钻会员）
wrangler secret put QQ_COOKIE
```

**替代方式：Cloudflare控制台设置**
1. 登录 [Cloudflare Workers](https://workers.cloudflare.com/)
2. 选择您的Worker
3. 在Settings → Environment Variables中添加：
   - `NETEASE_COOKIE`: 您的网易云音乐Cookie
   - `QQ_COOKIE`: 您的QQ音乐Cookie

#### 3. 部署

```bash
# 本地开发
npm run dev

# 部署到生产环境
npm run deploy
```

## 📁 项目结构

```
MusicApi/
├── src/
│   ├── index.js           # 主入口文件
│   ├── router.js          # API路由系统
│   ├── web-server.js      # Web界面服务
│   ├── apis/
│   │   ├── netease.js     # 网易云音乐API
│   │   └── qq-music.js    # QQ音乐API
│   ├── utils/
│   │   ├── index.js       # 工具模块导出
│   │   ├── url-parser.js  # URL解析器
│   │   ├── crypto.js      # 加密算法
│   │   ├── cookie-manager.js # Cookie管理
│   │   └── http-client.js # HTTP客户端
│   └── web/
│       ├── interface.html # Web界面模板
│       ├── styles.css     # 界面样式
│       └── script.js      # 交互脚本
├── wrangler.toml          # CF Workers配置
├── package.json           # 项目依赖
└── README.md              # 项目文档
```

## 🌐 API接口

### 网易云音乐API

| 接口 | 方法 | 说明 | 示例 |
|------|------|------|------|
| `/api/netease/search` | GET | 搜索歌曲 | `?keywords=周杰伦&limit=10` |
| `/api/netease/song` | POST | 单曲解析 | `{"url":"186016","level":"lossless","type":"json"}` |
| `/api/netease/playlist` | GET | 歌单解析 | `?id=123456789` |
| `/api/netease/album` | GET | 专辑解析 | `?id=18905` |

### QQ音乐API

| 接口 | 方法 | 说明 | 示例 |
|------|------|------|------|
| `/api/qq/song` | GET | 歌曲解析 | `?url=https://y.qq.com/n/ryqq/songDetail/xxx` |

### 系统接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/info` | GET | API信息 |
| `/` | GET | Web界面 |

## 🔧 高级配置

### 自定义域名

```bash
# 添加自定义域名
wrangler route add "music.yourdomain.com/*" music-parser
```

### 多环境部署

```bash
# 开发环境
wrangler dev

# 测试环境
wrangler deploy --env staging

# 生产环境
wrangler deploy --env production
```

### 性能监控

```bash
# 查看实时日志
wrangler tail

# 查看部署状态
wrangler status
```

## 🔒 安全说明

### Cookie获取和配置
1. **网易云音乐**：需要黑胶会员Cookie
2. **QQ音乐**：需要绿钻会员Cookie
3. **安全建议**：
   - 定期更换Cookie
   - 使用Wrangler Secrets存储敏感信息
   - 不要在代码中硬编码Cookie

### 使用限制
- 仅供学习和个人使用
- 请遵守相关平台的使用条款
- 禁止商业用途和大规模爬取
- 建议控制请求频率

## 💰 成本说明

### Cloudflare Workers免费额度
- **请求数**：100,000次/天
- **CPU时间**：10ms/请求
- **内存**：128MB

### 预估使用成本
- **个人使用**：通常在免费额度内
- **小型项目**：约$5-10/月
- **中型项目**：约$20-50/月

## 📖 详细文档

- 📚 **[部署指南](../DEPLOY.md)** - 详细的部署步骤和配置说明
- 🔧 **[API文档](../API.md)** - 完整的API接口说明和使用示例
- 🆘 **[故障排除](../TROUBLESHOOTING.md)** - 常见问题和解决方案
- ⚙️ **[配置示例](../example.env)** - 环境变量配置模板

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 🙏 致谢

本项目基于以下优秀的开源项目：
- [Netease_url](https://github.com/Suxiaoqinx/Netease_url) - 网易云音乐解析
- [tencent_url](https://github.com/Suxiaoqinx/tencent_url) - QQ音乐解析

## ⭐ Star History

如果这个项目对您有帮助，请给个Star支持一下！

[![Star History Chart](https://api.star-history.com/svg?repos=your-repo/music-parser&type=Date)](https://star-history.com/#your-repo/music-parser&Date)

## 📝 文件整理说明

由于项目文件较多，您需要将根目录下的 `src/` 文件夹中的所有文件复制到 `MusicApi/src/` 目录中：

```bash
# 复制所有源文件到MusicApi目录
cp -r ../src/* ./src/

# 或者手动复制以下文件：
# ../src/web-server.js → ./src/web-server.js
# ../src/apis/ → ./src/apis/
# ../src/utils/ → ./src/utils/
# ../src/web/ → ./src/web/
```

复制完成后，您就可以在 `MusicApi` 目录中进行部署了。
