{"name": "music-parser", "version": "1.0.0", "description": "网易云音乐和QQ音乐解析工具 - Cloudflare Workers版本", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["music", "parser", "netease", "qq-music", "cloudflare-workers"], "author": "Claude 4.0 sonnet", "license": "MIT", "devDependencies": {"wrangler": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}