/**
 * Web界面服务模块
 * 提供静态资源服务和界面渲染
 */

export class WebServer {
  /**
   * 处理Web界面请求
   * @param {Request} request - 请求对象
   * @param {Object} env - 环境变量
   * @returns {Promise<Response>} 响应对象
   */
  static async handleWebRequest(request, env) {
    const url = new URL(request.url);

    // 根路径返回主界面
    if (url.pathname === '/') {
      return this.serveMainInterface();
    }

    // 404处理
    return this.serve404Page();
  }

  /**
   * 服务主界面
   * @returns {Response} HTML响应
   */
  static serveMainInterface() {
    const html = this.getMainInterfaceHTML();

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    });
  }

  /**
   * 获取主界面HTML内容
   * @returns {string} 完整的HTML内容
   */
  static getMainInterfaceHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐解析工具 - 支持网易云音乐和QQ音乐</title>
    <meta name="description" content="支持网易云音乐和QQ音乐的在线解析工具，提供多种音质下载">
    <meta name="keywords" content="网易云音乐,QQ音乐,音乐下载,无损音质,音乐解析">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- APlayer CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css">

    <style>
        \${this.getInlineCSS()}
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <button class="theme-toggle" id="themeToggle" title="切换主题">
        <i class="bi bi-sun-fill" id="themeIcon"></i>
    </button>

    <div class="container py-4">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-white mb-3">
                <i class="bi bi-music-note-beamed me-3"></i>
                音乐解析工具
            </h1>
            <p class="lead text-white-50">支持网易云音乐和QQ音乐的高品质音乐解析</p>
        </div>

        <!-- 平台选择 -->
        <div class="text-center mb-4">
            <button class="platform-tab netease active" data-platform="netease">
                <i class="bi bi-music-note me-2"></i>网易云音乐
            </button>
            <button class="platform-tab qq" data-platform="qq">
                <i class="bi bi-disc me-2"></i>QQ音乐
            </button>
        </div>

        <!-- 主要功能区域 -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- 网易云音乐功能 -->
                <div id="netease-panel" class="platform-panel">
                    <!-- 功能选择卡片 -->
                    <div class="glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-gear-fill me-2"></i>功能选择
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn active" data-feature="search">
                                        <i class="bi bi-search me-2"></i>歌曲搜索
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="parse">
                                        <i class="bi bi-link-45deg me-2"></i>单曲解析
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="playlist">
                                        <i class="bi bi-collection-play me-2"></i>歌单解析
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="album">
                                        <i class="bi bi-vinyl me-2"></i>专辑解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索功能 -->
                    <div id="search-feature" class="feature-panel glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-search me-2"></i>歌曲搜索
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" id="searchKeywords" class="form-control" placeholder="输入歌曲名、歌手或专辑名">
                                </div>
                                <div class="col-md-4">
                                    <input type="number" id="searchLimit" class="form-control" value="10" min="1" max="50" placeholder="数量">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-success btn-modern w-100" id="searchBtn">
                                        <i class="bi bi-search me-2"></i>开始搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 单曲解析功能 -->
                    <div id="parse-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-link-45deg me-2"></i>单曲解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="songUrl" class="form-control" placeholder="输入歌曲ID或网易云音乐链接">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">音质选择</label>
                                    <div class="quality-selector">
                                        <span class="quality-badge bg-primary text-white active" data-quality="standard">标准</span>
                                        <span class="quality-badge bg-info text-white" data-quality="exhigh">极高</span>
                                        <span class="quality-badge bg-success text-white" data-quality="lossless">无损</span>
                                        <span class="quality-badge bg-warning text-dark" data-quality="hires">Hi-Res</span>
                                        <span class="quality-badge bg-danger text-white" data-quality="sky">环绕声</span>
                                        <span class="quality-badge bg-dark text-white" data-quality="jymaster">母带</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-primary btn-modern w-100" id="parseBtn">
                                        <i class="bi bi-download me-2"></i>开始解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 歌单解析功能 -->
                    <div id="playlist-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-collection-play me-2"></i>歌单解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="playlistUrl" class="form-control" placeholder="输入歌单ID或网易云歌单链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-warning btn-modern w-100" id="playlistBtn">
                                        <i class="bi bi-collection-play me-2"></i>解析歌单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 专辑解析功能 -->
                    <div id="album-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-vinyl me-2"></i>专辑解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="albumUrl" class="form-control" placeholder="输入专辑ID或网易云专辑链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-info btn-modern w-100" id="albumBtn">
                                        <i class="bi bi-vinyl me-2"></i>解析专辑
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QQ音乐功能 -->
                <div id="qq-panel" class="platform-panel" style="display: none;">
                    <div class="glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-disc me-2"></i>QQ音乐解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="qqSongUrl" class="form-control" placeholder="输入QQ音乐歌曲链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-primary btn-modern w-100" id="qqParseBtn">
                                        <i class="bi bi-download me-2"></i>开始解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载动画 -->
                <div class="loading-spinner" id="loadingSpinner"></div>

                <!-- 结果展示区域 -->
                <div id="resultArea"></div>
            </div>
        </div>
    </div>

    <!-- 浮动播放器 -->
    <div class="floating-player" id="floatingPlayer">
        <div id="aplayer"></div>
    </div>

    <!-- 大图预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="预览图片" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-4 mt-5">
        <div class="container">
            <p class="text-white-50 mb-0">
                <i class="bi bi-heart-fill text-danger me-1"></i>
                音乐解析工具 &copy; 2025 |
                <a href="https://github.com" target="_blank" class="text-white-50 text-decoration-none">
                    <i class="bi bi-github me-1"></i>GitHub
                </a>
            </p>
        </div>
    </footer>

    <!-- JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js"></script>

    <script>
        \${this.getInlineJS()}
    </script>
</body>
</html>\`;
  }

  /**
   * 获取内联CSS样式
   * @returns {string} CSS样式内容
   */
  static getInlineCSS() {
    return \`:root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --netease-color: #c20c0c;
            --qq-color: #12b7f5;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
            --border-radius: 12px;
            --transition: all 0.3s ease;
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] {
            --primary-color: #0d6efd;
            --light-color: #212529;
            --dark-color: #f8f9fa;
            --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --card-shadow: 0 4px 6px rgba(255, 255, 255, 0.1);
            --card-shadow-hover: 0 8px 15px rgba(255, 255, 255, 0.2);
            --glass-bg: rgba(33, 37, 41, 0.95);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            transition: var(--transition);
        }

        body {
            background: var(--gradient-bg);
            color: var(--dark-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        [data-theme="dark"] body {
            color: var(--light-color);
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }

        .glass-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .platform-tab {
            border: none;
            background: transparent;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
            margin: 0 8px;
            color: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .platform-tab:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateY(-2px);
        }

        .platform-tab.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            border-color: var(--primary-color);
        }

        .platform-tab.netease.active {
            background: var(--netease-color);
            box-shadow: 0 4px 12px rgba(194, 12, 12, 0.3);
            border-color: var(--netease-color);
        }

        .platform-tab.qq.active {
            background: var(--qq-color);
            box-shadow: 0 4px 12px rgba(18, 183, 245, 0.3);
            border-color: var(--qq-color);
        }

        .feature-btn {
            border-radius: var(--border-radius);
            padding: 15px;
            font-weight: 600;
            transition: var(--transition);
            border: 2px solid var(--primary-color);
            background: transparent;
            color: var(--primary-color);
        }

        .feature-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .feature-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn-modern {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            border: none;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .quality-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .quality-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
            user-select: none;
        }

        .quality-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .quality-badge.active {
            border-color: white;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
        }

        .loading-spinner {
            display: none;
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 30px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            transition: var(--transition);
            border: none;
        }

        [data-theme="dark"] .result-card {
            background: #34495e;
            color: var(--light-color);
        }

        .result-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .song-cover {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: var(--transition);
        }

        .song-cover:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            color: var(--dark-color);
        }

        [data-theme="dark"] .theme-toggle {
            background: rgba(33, 37, 41, 0.9);
            color: var(--light-color);
        }

        .theme-toggle:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: var(--card-shadow-hover);
        }

        .search-result-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid rgba(0,0,0,0.1);
        }

        [data-theme="dark"] .search-result-item {
            background: #2c3e50;
            color: var(--light-color);
            border-color: rgba(255,255,255,0.1);
        }

        .search-result-item:hover {
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .floating-player {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow-hover);
            z-index: 1000;
            display: none;
            overflow: hidden;
        }

        [data-theme="dark"] .floating-player {
            background: #2c3e50;
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-left {
            animation: slideInLeft 0.5s ease-out;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .bounce-in {
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid rgba(0,0,0,0.1);
            padding: 12px 16px;
            transition: var(--transition);
            background: rgba(255,255,255,0.9);
        }

        [data-theme="dark"] .form-control {
            background: rgba(33, 37, 41, 0.9);
            border-color: rgba(255,255,255,0.1);
            color: var(--light-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            background: white;
        }

        [data-theme="dark"] .form-control:focus {
            background: #2c3e50;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .platform-tab {
                padding: 10px 20px;
                margin: 0 4px;
                font-size: 0.9em;
            }

            .glass-card .card-body {
                padding: 20px 15px;
            }

            .floating-player {
                width: calc(100% - 40px);
                left: 20px;
                right: 20px;
                bottom: 10px;
            }

            .theme-toggle {
                top: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
            }

            .quality-selector {
                justify-content: center;
            }

            .quality-badge {
                padding: 6px 12px;
                font-size: 0.8em;
            }

            .btn-modern {
                padding: 10px 25px;
                font-size: 0.9em;
            }

            .song-cover {
                width: 60px;
                height: 60px;
            }
        }\`;
  }

  /**
   * 获取内联JavaScript代码
   * @returns {string} JavaScript代码内容
   */
  static getInlineJS() {
    return \`
class MusicParser {
    constructor() {
        this.currentPlatform = 'netease';
        this.currentFeature = 'search';
        this.currentQuality = 'standard';
        this.aplayer = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTheme();
        this.showWelcomeMessage();
    }

    bindEvents() {
        // 平台切换
        document.querySelectorAll('.platform-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchPlatform(e.target.dataset.platform));
        });

        // 功能切换
        document.querySelectorAll('.feature-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchFeature(e.target.dataset.feature));
        });

        // 音质选择
        document.querySelectorAll('.quality-badge').forEach(badge => {
            badge.addEventListener('click', (e) => this.selectQuality(e.target.dataset.quality));
        });

        // 主题切换
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', () => this.searchSongs());
        document.getElementById('searchKeywords').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchSongs();
        });

        // 解析功能
        document.getElementById('parseBtn').addEventListener('click', () => this.parseSong());
        document.getElementById('songUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseSong();
        });

        // 歌单解析
        document.getElementById('playlistBtn').addEventListener('click', () => this.parsePlaylist());
        document.getElementById('playlistUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parsePlaylist();
        });

        // 专辑解析
        document.getElementById('albumBtn').addEventListener('click', () => this.parseAlbum());
        document.getElementById('albumUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseAlbum();
        });

        // QQ音乐解析
        document.getElementById('qqParseBtn').addEventListener('click', () => this.parseQQSong());
        document.getElementById('qqSongUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseQQSong();
        });
    }

    switchPlatform(platform) {
        this.currentPlatform = platform;

        // 更新标签状态
        document.querySelectorAll('.platform-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(\`[data-platform="\${platform}"]\`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.platform-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        document.getElementById(\`\${platform}-panel\`).style.display = 'block';

        // 清空结果
        this.clearResults();

        this.showNotification(\`已切换到\${platform === 'netease' ? '网易云音乐' : 'QQ音乐'}\`, 'info');
    }

    switchFeature(feature) {
        this.currentFeature = feature;

        // 更新按钮状态
        document.querySelectorAll('.feature-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(\`[data-feature="\${feature}"]\`).classList.add('active');

        // 切换功能面板
        document.querySelectorAll('.feature-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        document.getElementById(\`\${feature}-feature\`).style.display = 'block';

        // 清空结果
        this.clearResults();
    }

    selectQuality(quality) {
        this.currentQuality = quality;

        // 更新音质选择状态
        document.querySelectorAll('.quality-badge').forEach(badge => {
            badge.classList.remove('active');
        });
        document.querySelector(\`[data-quality="\${quality}"]\`).classList.add('active');
    }

    async searchSongs() {
        const keywords = document.getElementById('searchKeywords').value.trim();
        const limit = document.getElementById('searchLimit').value || 10;

        if (!keywords) {
            this.showNotification('请输入搜索关键词', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch(\`/api/netease/search?keywords=\${encodeURIComponent(keywords)}&limit=\${limit}\`);
            const data = await response.json();

            if (data.status === 200 && data.result.length > 0) {
                this.displaySearchResults(data.result);
                this.showNotification(\`找到 \${data.result.length} 首歌曲\`, 'success');
            } else {
                this.showNotification('未找到相关歌曲', 'info');
                this.clearResults();
            }
        } catch (error) {
            this.showNotification('搜索失败，请重试', 'error');
            console.error('Search error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseSong() {
        const url = document.getElementById('songUrl').value.trim();

        if (!url) {
            this.showNotification('请输入歌曲ID或链接', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch('/api/netease/song', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    url: url,
                    level: this.currentQuality,
                    type: 'json'
                })
            });

            const data = await response.json();

            if (data.status === 200) {
                this.displaySongResult(data);
                this.showNotification('解析成功！', 'success');
            } else {
                this.showNotification(data.error || '解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('解析失败，请重试', 'error');
            console.error('Parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseQQSong() {
        const url = document.getElementById('qqSongUrl').value.trim();

        if (!url) {
            this.showNotification('请输入QQ音乐歌曲链接', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch(\`/api/qq/song?url=\${encodeURIComponent(url)}\`);
            const data = await response.json();

            if (data.song && !data.song.msg) {
                this.displayQQSongResult(data);
                this.showNotification('QQ音乐解析成功！', 'success');
            } else {
                this.showNotification(data.song?.msg || 'QQ音乐解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('QQ音乐解析失败，请重试', 'error');
            console.error('QQ parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    displaySearchResults(results) {
        const resultArea = document.getElementById('resultArea');

        let html = \`
            <div class="glass-card fade-in">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-search me-2"></i>搜索结果 (\${results.length}首)
                    </h5>
                    <div class="search-results">
        \`;

        results.forEach((song, index) => {
            html += \`
                <div class="search-result-item slide-in-left" style="animation-delay: \${index * 0.1}s" onclick="musicParser.selectSongFromSearch('\${song.id}', '\${song.name}')">
                    <div class="d-flex align-items-center">
                        <img src="\${song.picUrl || '/default-cover.jpg'}" alt="封面" class="song-cover me-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">\${song.name}</h6>
                            <p class="mb-1 text-muted">\${song.artists}</p>
                            <small class="text-secondary">\${song.album}</small>
                        </div>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-play-fill me-1"></i>解析
                        </button>
                    </div>
                </div>
            \`;
        });

        html += \`
                    </div>
                </div>
            </div>
        \`;

        resultArea.innerHTML = html;
    }

    selectSongFromSearch(songId, songName) {
        document.getElementById('songUrl').value = songId;
        this.switchFeature('parse');
        this.showNotification(\`已选择歌曲：\${songName}\`, 'info');

        // 滚动到解析区域
        document.getElementById('parse-feature').scrollIntoView({ behavior: 'smooth' });
    }

    displaySongResult(data) {
        const resultArea = document.getElementById('resultArea');

        const html = \`
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="\${data.pic}" alt="封面" class="song-cover mb-3" style="width: 120px; height: 120px;" onclick="musicParser.showImageModal('\${data.pic}')">
                        </div>
                        <div class="col-md-9">
                            <h4 class="fw-bold mb-2">\${data.name}</h4>
                            <p class="mb-2"><span class="badge bg-primary me-2">歌手</span>\${data.ar_name}</p>
                            <p class="mb-2"><span class="badge bg-secondary me-2">专辑</span>\${data.al_name}</p>
                            <p class="mb-2"><span class="badge bg-success me-2">音质</span>\${data.level}</p>
                            <p class="mb-2"><span class="badge bg-warning text-dark me-2">大小</span>\${data.size}</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="\${data.url}" target="_blank" class="btn btn-primary btn-modern">
                                    <i class="bi bi-download me-2"></i>下载音乐
                                </a>
                                <button class="btn btn-success btn-modern" onclick="musicParser.playMusic('\${data.url}', '\${data.name}', '\${data.ar_name}', '\${data.pic}', '\${data.lyric}')">
                                    <i class="bi bi-play-fill me-2"></i>在线播放
                                </button>
                            </div>
                        </div>
                    </div>
                    \${data.lyric ? \`
                        <div class="mt-4">
                            <h6><i class="bi bi-music-note me-2"></i>歌词</h6>
                            <div class="lyric-box p-3 bg-light rounded" style="max-height: 200px; overflow-y: auto;">
                                \${this.formatLyrics(data.lyric, data.tlyric)}
                            </div>
                        </div>
                    \` : ''}
                </div>
            </div>
        \`;

        resultArea.innerHTML = html;
    }

    displayQQSongResult(data) {
        const resultArea = document.getElementById('resultArea');
        const song = data.song;
        const musicUrls = data.music_urls;

        let urlsHtml = '';
        Object.entries(musicUrls).forEach(([format, info]) => {
            if (info && info.url) {
                urlsHtml += \`
                    <a href="\${info.url}" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                        \${format.toUpperCase()} (\${info.bitrate})
                    </a>
                \`;
            }
        });

        const html = \`
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="\${song.pic}" alt="封面" class="song-cover mb-3" style="width: 120px; height: 120px;" onclick="musicParser.showImageModal('\${song.pic}')">
                        </div>
                        <div class="col-md-9">
                            <h4 class="fw-bold mb-2">\${song.name}</h4>
                            <p class="mb-2"><span class="badge bg-info me-2">歌手</span>\${song.singer}</p>
                            <p class="mb-2"><span class="badge bg-secondary me-2">专辑</span>\${song.album}</p>
                            <div class="mb-3">
                                <h6>可用格式：</h6>
                                \${urlsHtml || '<span class="text-muted">暂无可用下载链接</span>'}
                            </div>
                        </div>
                    </div>
                    \${data.lyric && data.lyric.lyric ? \`
                        <div class="mt-4">
                            <h6><i class="bi bi-music-note me-2"></i>歌词</h6>
                            <div class="lyric-box p-3 bg-light rounded" style="max-height: 200px; overflow-y: auto;">
                                \${data.lyric.lyric.replace(/\\n/g, '<br>')}
                            </div>
                        </div>
                    \` : ''}
                </div>
            </div>
        \`;

        resultArea.innerHTML = html;
    }

    formatLyrics(lyric, tlyric) {
        if (!lyric) return '';
        return lyric.replace(/\\n/g, '<br>');
    }

    playMusic(url, name, artist, cover, lyric) {
        // 销毁现有播放器
        if (this.aplayer) {
            this.aplayer.destroy();
        }

        // 显示浮动播放器
        const floatingPlayer = document.getElementById('floatingPlayer');
        floatingPlayer.style.display = 'block';
        floatingPlayer.classList.add('bounce-in');

        // 创建新播放器
        this.aplayer = new APlayer({
            container: document.getElementById('aplayer'),
            lrcType: 1,
            audio: [{
                name: name,
                artist: artist,
                url: url,
                cover: cover,
                lrc: lyric || ''
            }]
        });

        this.showNotification('开始播放音乐', 'success');
    }

    showImageModal(imageUrl) {
        document.getElementById('modalImage').src = imageUrl;
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }

    clearResults() {
        document.getElementById('resultArea').innerHTML = '';
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = \`alert alert-\${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed\`;
        notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = \`
            \${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        \`;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);

        const icon = document.getElementById('themeIcon');
        icon.className = theme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
    }

    showWelcomeMessage() {
        setTimeout(() => {
            this.showNotification('欢迎使用音乐解析工具！支持网易云音乐和QQ音乐', 'info');
        }, 1000);
    }

    async parsePlaylist() {
        const url = document.getElementById('playlistUrl').value.trim();

        if (!url) {
            this.showNotification('请输入歌单ID或链接', 'warning');
            return;
        }

        // 提取歌单ID
        let playlistId = url;
        const idMatch = url.match(/playlist\\?id=(\\d+)/);
        if (idMatch) playlistId = idMatch[1];

        this.showLoading(true);

        try {
            const response = await fetch(\`/api/netease/playlist?id=\${playlistId}\`);
            const data = await response.json();

            if (data.status === 200) {
                this.displayPlaylistResult(data.playlist);
                this.showNotification('歌单解析成功！', 'success');
            } else {
                this.showNotification(data.error || '歌单解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('歌单解析失败，请重试', 'error');
            console.error('Playlist parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseAlbum() {
        const url = document.getElementById('albumUrl').value.trim();

        if (!url) {
            this.showNotification('请输入专辑ID或链接', 'warning');
            return;
        }

        // 提取专辑ID
        let albumId = url;
        const idMatch = url.match(/album\\?id=(\\d+)/);
        if (idMatch) albumId = idMatch[1];

        this.showLoading(true);

        try {
            const response = await fetch(\`/api/netease/album?id=\${albumId}\`);
            const data = await response.json();

            if (data.status === 200) {
                this.displayAlbumResult(data.album);
                this.showNotification('专辑解析成功！', 'success');
            } else {
                this.showNotification(data.error || '专辑解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('专辑解析失败，请重试', 'error');
            console.error('Album parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    displayPlaylistResult(playlist) {
        const resultArea = document.getElementById('resultArea');

        let tracksHtml = '';
        playlist.tracks.forEach((track, index) => {
            tracksHtml += \`
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <img src="\${track.picUrl}" alt="封面" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <div>
                            <h6 class="mb-1">\${index + 1}. \${track.name}</h6>
                            <small class="text-muted">\${track.artists} - \${track.album}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="musicParser.selectSongFromSearch('\${track.id}', '\${track.name}')">
                        解析
                    </button>
                </div>
            \`;
        });

        const html = \`
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="\${playlist.coverImgUrl}" alt="歌单封面" class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                        <div>
                            <h4 class="fw-bold mb-1">\${playlist.name}</h4>
                            <p class="text-muted mb-1">创建者：\${playlist.creator}</p>
                            <small class="text-secondary">共 \${playlist.trackCount} 首歌曲</small>
                        </div>
                    </div>
                    \${playlist.description ? \`<p class="text-secondary mb-3">\${playlist.description}</p>\` : ''}
                    <div class="list-group">
                        \${tracksHtml}
                    </div>
                </div>
            </div>
        \`;

        resultArea.innerHTML = html;
    }

    displayAlbumResult(album) {
        const resultArea = document.getElementById('resultArea');

        let songsHtml = '';
        album.songs.forEach((song, index) => {
            songsHtml += \`
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <img src="\${song.picUrl}" alt="封面" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <div>
                            <h6 class="mb-1">\${index + 1}. \${song.name}</h6>
                            <small class="text-muted">\${song.artists}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="musicParser.selectSongFromSearch('\${song.id}', '\${song.name}')">
                        解析
                    </button>
                </div>
            \`;
        });

        const html = \`
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="\${album.coverImgUrl}" alt="专辑封面" class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                        <div>
                            <h4 class="fw-bold mb-1">\${album.name}</h4>
                            <p class="text-muted mb-1">艺术家：\${album.artist}</p>
                            <small class="text-secondary">共 \${album.songs.length} 首歌曲</small>
                        </div>
                    </div>
                    \${album.description ? \`<p class="text-secondary mb-3">\${album.description}</p>\` : ''}
                    <div class="list-group">
                        \${songsHtml}
                    </div>
                </div>
            </div>
        \`;

        resultArea.innerHTML = html;
    }
}

// 初始化应用
const musicParser = new MusicParser();
\`;
  }

  /**
   * 服务404页面
   * @returns {Response} 404响应
   */
  static serve404Page() {
    const html = \`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 音乐解析工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px 20px;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
        }
        h1 {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        a {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 12px 24px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>404</h1>
        <p>抱歉，您访问的页面不存在。</p>
        <a href="/">返回首页</a>
    </div>
</body>
</html>\`;

    return new Response(html, {
      status: 404,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  }
}