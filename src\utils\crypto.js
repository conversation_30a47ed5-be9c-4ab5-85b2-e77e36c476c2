/**
 * 加密工具类
 * 移植Python版本的加密算法到Web Crypto API
 */

export class CryptoUtils {
  /**
   * 将字节数组转换为十六进制字符串
   * 移植自Python版本的HexDigest()函数
   * @param {Uint8Array} data - 字节数组
   * @returns {string} 十六进制字符串
   */
  static hexDigest(data) {
    return Array.from(data)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * 计算文本的MD5哈希值
   * 移植自Python版本的HashDigest()函数
   * @param {string} text - 待哈希的文本
   * @returns {Promise<Uint8Array>} MD5哈希值字节数组
   */
  static async hashDigest(text) {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await crypto.subtle.digest('MD5', data);
    return new Uint8Array(hashBuffer);
  }

  /**
   * 计算文本的MD5哈希值并转换为十六进制字符串
   * 移植自Python版本的HashHexDigest()函数
   * @param {string} text - 待哈希的文本
   * @returns {Promise<string>} MD5哈希值的十六进制字符串
   */
  static async hashHexDigest(text) {
    const hashBytes = await this.hashDigest(text);
    return this.hexDigest(hashBytes);
  }

  /**
   * AES-ECB加密
   * 移植自Python版本的AES加密逻辑
   * @param {string} plaintext - 明文
   * @param {Uint8Array} key - 加密密钥
   * @returns {Promise<Uint8Array>} 加密后的字节数组
   */
  static async aesEncrypt(plaintext, key) {
    try {
      // 导入密钥
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        key,
        { name: 'AES-ECB' },
        false,
        ['encrypt']
      );

      // PKCS7填充
      const encoder = new TextEncoder();
      const data = encoder.encode(plaintext);
      const paddedData = this.pkcs7Pad(data, 16);

      // 加密
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-ECB' },
        cryptoKey,
        paddedData
      );

      return new Uint8Array(encrypted);
    } catch (error) {
      throw new Error(`AES加密失败: ${error.message}`);
    }
  }

  /**
   * PKCS7填充
   * @param {Uint8Array} data - 原始数据
   * @param {number} blockSize - 块大小
   * @returns {Uint8Array} 填充后的数据
   */
  static pkcs7Pad(data, blockSize) {
    const padLength = blockSize - (data.length % blockSize);
    const padded = new Uint8Array(data.length + padLength);
    padded.set(data);
    
    // 填充字节
    for (let i = data.length; i < padded.length; i++) {
      padded[i] = padLength;
    }
    
    return padded;
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  static generateRandomString(length) {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成随机数字字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机数字字符串
   */
  static generateRandomNumbers(length) {
    const chars = '0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Base64编码
   * @param {Uint8Array} data - 字节数组
   * @returns {string} Base64字符串
   */
  static base64Encode(data) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;
    
    while (i < data.length) {
      const a = data[i++];
      const b = i < data.length ? data[i++] : 0;
      const c = i < data.length ? data[i++] : 0;
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += i - 2 < data.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      result += i - 1 < data.length ? chars.charAt(bitmap & 63) : '=';
    }
    
    return result;
  }

  /**
   * Base64解码
   * @param {string} base64 - Base64字符串
   * @returns {Uint8Array} 字节数组
   */
  static base64Decode(base64) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    const lookup = new Uint8Array(256);
    
    for (let i = 0; i < chars.length; i++) {
      lookup[chars.charCodeAt(i)] = i;
    }
    
    const len = base64.length;
    const bufferLength = len * 0.75;
    const bytes = new Uint8Array(bufferLength);
    
    let p = 0;
    for (let i = 0; i < len; i += 4) {
      const encoded1 = lookup[base64.charCodeAt(i)];
      const encoded2 = lookup[base64.charCodeAt(i + 1)];
      const encoded3 = lookup[base64.charCodeAt(i + 2)];
      const encoded4 = lookup[base64.charCodeAt(i + 3)];
      
      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);
      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);
      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);
    }
    
    return bytes;
  }
}
