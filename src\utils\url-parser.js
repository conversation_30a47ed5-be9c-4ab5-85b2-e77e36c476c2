/**
 * URL解析工具类
 * 支持网易云音乐和QQ音乐的URL解析
 */

export class URLParser {
  /**
   * 解析网易云音乐URL，提取歌曲ID
   * 移植自Python版本的ids()函数
   * @param {string} url - 网易云音乐URL或ID
   * @returns {Promise<string>} 歌曲ID
   */
  static async parseNetease(url) {
    try {
      let ids = url;
      
      // 处理163cn.tv短链接重定向
      if (ids.includes('163cn.tv')) {
        const response = await fetch(ids, { 
          method: 'GET',
          redirect: 'manual' // 不自动跟随重定向
        });
        
        if (response.status >= 300 && response.status < 400) {
          const location = response.headers.get('Location');
          if (location) {
            ids = location;
          }
        }
      }
      
      // 从music.163.com URL中提取ID
      if (ids.includes('music.163.com')) {
        const idIndex = ids.indexOf('id=') + 3;
        if (idIndex > 2) {
          ids = ids.substring(idIndex).split('&')[0];
        }
      }
      
      return ids;
    } catch (error) {
      throw new Error(`网易云URL解析失败: ${error.message}`);
    }
  }

  /**
   * 解析QQ音乐URL，提取歌曲ID
   * 移植自Python版本的ids()函数
   * @param {string} url - QQ音乐URL
   * @returns {Promise<string>} 歌曲ID
   */
  static async parseQQ(url) {
    try {
      let processedUrl = url;
      
      // 处理c6.y.qq.com短链接重定向
      if (processedUrl.includes('c6.y.qq.com')) {
        const response = await fetch(processedUrl, {
          method: 'GET',
          redirect: 'manual'
        });
        
        if (response.status >= 300 && response.status < 400) {
          const location = response.headers.get('Location');
          if (location) {
            processedUrl = location;
          }
        }
      }
      
      // 检查是否为y.qq.com域名
      if (processedUrl.includes('y.qq.com')) {
        // 处理 /songDetail/ 形式的URL
        if (processedUrl.includes('/songDetail/')) {
          const startIndex = processedUrl.indexOf('/songDetail/') + '/songDetail/'.length;
          const songId = processedUrl.substring(startIndex).split('/')[0];
          return songId;
        }
        
        // 处理带id=参数的URL
        if (processedUrl.includes('id=')) {
          const idIndex = processedUrl.indexOf('id=') + 3;
          const songId = processedUrl.substring(idIndex).split('&')[0];
          return songId;
        }
      }
      
      // 如果都不匹配，返回null
      return null;
    } catch (error) {
      throw new Error(`QQ音乐URL解析失败: ${error.message}`);
    }
  }

  /**
   * 验证URL是否为有效的音乐链接
   * @param {string} url - 待验证的URL
   * @param {string} platform - 平台类型 ('netease' | 'qq')
   * @returns {boolean} 是否为有效链接
   */
  static isValidMusicUrl(url, platform) {
    if (!url || typeof url !== 'string') {
      return false;
    }
    
    switch (platform) {
      case 'netease':
        return url.includes('music.163.com') || url.includes('163cn.tv');
      case 'qq':
        return url.includes('y.qq.com') || url.includes('c6.y.qq.com');
      default:
        return false;
    }
  }

  /**
   * 自动检测URL所属平台
   * @param {string} url - 音乐URL
   * @returns {string|null} 平台类型或null
   */
  static detectPlatform(url) {
    if (this.isValidMusicUrl(url, 'netease')) {
      return 'netease';
    }
    if (this.isValidMusicUrl(url, 'qq')) {
      return 'qq';
    }
    return null;
  }
}
