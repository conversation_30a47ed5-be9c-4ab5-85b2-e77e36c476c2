<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐解析工具 - 支持网易云音乐和QQ音乐</title>
    <meta name="description" content="支持网易云音乐和QQ音乐的在线解析工具，提供多种音质下载">
    <meta name="keywords" content="网易云音乐,QQ音乐,音乐下载,无损音质,音乐解析">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- APlayer CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --netease-color: #c20c0c;
            --qq-color: #12b7f5;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        [data-theme="dark"] {
            --primary-color: #0d6efd;
            --light-color: #212529;
            --dark-color: #f8f9fa;
            --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --card-shadow: 0 4px 6px rgba(255, 255, 255, 0.1);
            --card-shadow-hover: 0 8px 15px rgba(255, 255, 255, 0.2);
        }

        * {
            transition: var(--transition);
        }

        body {
            background: var(--gradient-bg);
            color: var(--dark-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        [data-theme="dark"] body {
            color: var(--light-color);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }

        [data-theme="dark"] .glass-card {
            background: rgba(33, 37, 41, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .glass-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .platform-tab {
            border: none;
            background: transparent;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
            margin: 0 8px;
        }

        .platform-tab.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .platform-tab.netease.active {
            background: var(--netease-color);
            box-shadow: 0 4px 12px rgba(194, 12, 12, 0.3);
        }

        .platform-tab.qq.active {
            background: var(--qq-color);
            box-shadow: 0 4px 12px rgba(18, 183, 245, 0.3);
        }

        .feature-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
            border: none;
            transition: var(--transition);
        }

        [data-theme="dark"] .feature-card {
            background: #2c3e50;
            color: var(--light-color);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--card-shadow-hover);
        }

        .btn-modern {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            border: none;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .quality-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            margin: 2px;
            cursor: pointer;
            transition: var(--transition);
        }

        .quality-badge:hover {
            transform: scale(1.05);
        }

        .loading-spinner {
            display: none;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            transition: var(--transition);
        }

        [data-theme="dark"] .result-card {
            background: #34495e;
            color: var(--light-color);
        }

        .result-card:hover {
            box-shadow: var(--card-shadow-hover);
        }

        .song-cover {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }

        [data-theme="dark"] .theme-toggle {
            background: rgba(33, 37, 41, 0.9);
            color: var(--light-color);
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: var(--card-shadow-hover);
        }

        .search-result-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: var(--transition);
            cursor: pointer;
        }

        [data-theme="dark"] .search-result-item {
            background: #2c3e50;
            color: var(--light-color);
        }

        .search-result-item:hover {
            transform: translateX(8px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .floating-player {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow-hover);
            z-index: 1000;
            display: none;
        }

        [data-theme="dark"] .floating-player {
            background: #2c3e50;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .platform-tab {
                padding: 8px 16px;
                margin: 0 4px;
                font-size: 0.9em;
            }
            
            .feature-card {
                padding: 16px;
            }
            
            .floating-player {
                width: calc(100% - 40px);
                left: 20px;
                right: 20px;
            }
            
            .theme-toggle {
                top: 10px;
                right: 10px;
                width: 40px;
                height: 40px;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <button class="theme-toggle" id="themeToggle" title="切换主题">
        <i class="bi bi-sun-fill" id="themeIcon"></i>
    </button>

    <div class="container py-4">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-white mb-3">
                <i class="bi bi-music-note-beamed me-3"></i>
                音乐解析工具
            </h1>
            <p class="lead text-white-50">支持网易云音乐和QQ音乐的高品质音乐解析</p>
        </div>

        <!-- 平台选择 -->
        <div class="text-center mb-4">
            <button class="platform-tab netease active" data-platform="netease">
                <i class="bi bi-music-note me-2"></i>网易云音乐
            </button>
            <button class="platform-tab qq" data-platform="qq">
                <i class="bi bi-disc me-2"></i>QQ音乐
            </button>
        </div>

        <!-- 主要功能区域 -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- 网易云音乐功能 -->
                <div id="netease-panel" class="platform-panel">
                    <!-- 功能选择卡片 -->
                    <div class="glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-gear-fill me-2"></i>功能选择
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn active" data-feature="search">
                                        <i class="bi bi-search me-2"></i>歌曲搜索
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="parse">
                                        <i class="bi bi-link-45deg me-2"></i>单曲解析
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="playlist">
                                        <i class="bi bi-collection-play me-2"></i>歌单解析
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 feature-btn" data-feature="album">
                                        <i class="bi bi-vinyl me-2"></i>专辑解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索功能 -->
                    <div id="search-feature" class="feature-panel glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-search me-2"></i>歌曲搜索
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" id="searchKeywords" class="form-control" placeholder="输入歌曲名、歌手或专辑名">
                                </div>
                                <div class="col-md-4">
                                    <input type="number" id="searchLimit" class="form-control" value="10" min="1" max="50" placeholder="数量">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-success btn-modern w-100" id="searchBtn">
                                        <i class="bi bi-search me-2"></i>开始搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 单曲解析功能 -->
                    <div id="parse-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-link-45deg me-2"></i>单曲解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="songUrl" class="form-control" placeholder="输入歌曲ID或网易云音乐链接">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">音质选择</label>
                                    <div class="quality-selector">
                                        <span class="quality-badge bg-primary text-white active" data-quality="standard">标准</span>
                                        <span class="quality-badge bg-info text-white" data-quality="exhigh">极高</span>
                                        <span class="quality-badge bg-success text-white" data-quality="lossless">无损</span>
                                        <span class="quality-badge bg-warning text-dark" data-quality="hires">Hi-Res</span>
                                        <span class="quality-badge bg-danger text-white" data-quality="sky">环绕声</span>
                                        <span class="quality-badge bg-dark text-white" data-quality="jymaster">母带</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-primary btn-modern w-100" id="parseBtn">
                                        <i class="bi bi-download me-2"></i>开始解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 歌单解析功能 -->
                    <div id="playlist-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-collection-play me-2"></i>歌单解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="playlistUrl" class="form-control" placeholder="输入歌单ID或网易云歌单链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-warning btn-modern w-100" id="playlistBtn">
                                        <i class="bi bi-collection-play me-2"></i>解析歌单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 专辑解析功能 -->
                    <div id="album-feature" class="feature-panel glass-card mb-4 fade-in" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-vinyl me-2"></i>专辑解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="albumUrl" class="form-control" placeholder="输入专辑ID或网易云专辑链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-info btn-modern w-100" id="albumBtn">
                                        <i class="bi bi-vinyl me-2"></i>解析专辑
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QQ音乐功能 -->
                <div id="qq-panel" class="platform-panel" style="display: none;">
                    <div class="glass-card mb-4 fade-in">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-disc me-2"></i>QQ音乐解析
                            </h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <input type="text" id="qqSongUrl" class="form-control" placeholder="输入QQ音乐歌曲链接">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-primary btn-modern w-100" id="qqParseBtn">
                                        <i class="bi bi-download me-2"></i>开始解析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载动画 -->
                <div class="loading-spinner" id="loadingSpinner"></div>

                <!-- 结果展示区域 -->
                <div id="resultArea"></div>
            </div>
        </div>
    </div>

    <!-- 浮动播放器 -->
    <div class="floating-player" id="floatingPlayer">
        <div id="aplayer"></div>
    </div>

    <!-- 大图预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="预览图片" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-4 mt-5">
        <div class="container">
            <p class="text-white-50 mb-0">
                <i class="bi bi-heart-fill text-danger me-1"></i>
                音乐解析工具 &copy; 2025 | 
                <a href="https://github.com" target="_blank" class="text-white-50 text-decoration-none">
                    <i class="bi bi-github me-1"></i>GitHub
                </a>
            </p>
        </div>
    </footer>

    <!-- JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js"></script>
    
    <!-- 内联JavaScript将在后续添加 -->
    <script>
        // JavaScript代码将在script.js中实现
    </script>
</body>
</html>
